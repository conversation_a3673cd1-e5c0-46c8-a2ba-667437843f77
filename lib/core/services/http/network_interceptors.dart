import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/utils/aes_encryption.dart';
import 'package:gp_stock_app/core/utils/host_util.dart';
import 'package:gp_stock_app/core/utils/language_util.dart';
import 'package:gp_stock_app/core/services/http/network_reachability.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'network_error.dart';

class CommonInterceptors extends InterceptorsWrapper {
  static bool _isFetchingHost = false;
  static DateTime? _lastFetchTime;
  static const Duration _fetchCooldown = Duration(seconds: 30);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    _appendCommonHeaders(options.headers);
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (response.data == null) {
      throw DioException(
        requestOptions: response.requestOptions,
        error: NetException.dataNotFound.message,
        type: DioExceptionType.badResponse,
      );
    }

    try {
      if (!kDebugMode) {
        final decryptedData = AESEncryption().decryptResponse(response.data);
        response.data = decryptedData;
      }
    } catch (e) {
      throw FormatException(
          "Failed to decode response/响应解析失败: path:${response.requestOptions.path} err: ${e.toString()}", response.data);
    }

    if (response.requestOptions.path == ApiEndpoints.login) {
      final token = response.headers['authorization']?.first;
      if (token != null) {
        getIt<UserCubit>().setToken(token);
      }
    }

    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    GPEasyLoading.dismiss();
    if (err.response?.statusCode == 401 && err.requestOptions.path != ApiEndpoints.login) {
      _handleUnauthorizedError();
    }
    if (err.response?.statusCode == 403) {
      LogE('403 Forbidden: ${err.requestOptions.path}');
      _handleForbiddenError();
    }
    if (kDebugMode && err.type != DioExceptionType.cancel && !kIgnoreApiList.contains(err.requestOptions.path)) {
      String errorMsg = _getErrorMessage(err);
      GPEasyLoading.showToast(errorMsg);
    }

    if (isServerUnreachable(err)) {
      _maybeFetchNewHost();
    }

    super.onError(err, handler);
  }

  Future<void> fetchNewHost() async {
    final url = await HostUtil().fetchHostFromOss();
    if (url != null) {
      NetworkProvider().resetBaseUrl(url);
      Http().resetBaseUrl(url);
    }
  }

  // 判定逻辑已抽取到 core/utils/network_reachability.dart

  void _maybeFetchNewHost() {
    if (_isFetchingHost) return;
    final now = DateTime.now();
    if (_lastFetchTime != null && now.difference(_lastFetchTime!) < _fetchCooldown) return;

    _isFetchingHost = true;
    fetchNewHost().whenComplete(() {
      _lastFetchTime = DateTime.now();
      _isFetchingHost = false;
    });
  }

  /// 往header添加动态公参
  void _appendCommonHeaders(Map<String, dynamic> headers) {
    headers['Accept-Language'] = LanguageUtil.getAcceptLanguageHeader();
    if (getIt.isRegistered<UserCubit>()) {
      final userState = getIt<UserCubit>().state;
      if (userState.isLogin) {
        headers['Authorization'] = userState.token;
      }
    }
  }

  /// 获取错误消息
  String _getErrorMessage(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return '连接超时，请检查网络';
      case DioExceptionType.sendTimeout:
        return '请求超时，请稍后再试';
      case DioExceptionType.receiveTimeout:
        return '服务器响应超时，请稍后再试';
      case DioExceptionType.badCertificate:
        return '证书验证失败，请检查网络安全设置';
      case DioExceptionType.badResponse:
        return _handleDioBadResponse(error.response);
      case DioExceptionType.cancel:
        return '请求已取消';
      case DioExceptionType.connectionError:
        return '网络连接错误，请检查网络设置';
      case DioExceptionType.unknown:
        return '发生未知错误，请稍后重试';
    }
  }

  String _handleDioBadResponse(Response? response) {
    if (response == null) return '服务器无响应，请稍后再试';

    switch (response.statusCode) {
      case 400:
        return '请求参数错误';
      case 401:
        return '未授权，请重新登录';
      case 403:
        return '没有权限访问此资源';
      case 404:
        return '请求的资源不存在';
      case 500:
        return '服务器内部错误，请稍后再试';
      default:
        return '请求失败，状态码：${response.statusCode}';
    }
  }

  Future<void> _handleUnauthorizedError() async {
    GPEasyLoading.dismiss();
    getIt<UserCubit>().logout();
    NetworkHelper.handleMessage(
      'sessionExpired'.tr(),
      type: HandleTypes.customDialog,
      snackBarType: SnackBarType.error,
      dialogKey: 'unauthorized_error',
      onTap: () {
        getIt<NavigatorService>().pushNamedAndRemoveUntil(AppRouter.routeLogin);
      },
    );
  }

  Future<void> _handleForbiddenError() async {
    GPEasyLoading.dismiss();
    getIt<UserCubit>().logout();
    getIt<NavigatorService>().pushReplace(AppRouter.routeProhibition, clearStack: true);
  }
}

class LogsInterceptors extends InterceptorsWrapper {
  final List<String> ignoredUrls;

  LogsInterceptors({this.ignoredUrls = const []});

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (!_shouldIgnore(options)) {
      LogD("REQUEST[${options.method}] => PATH: ${options.path}");
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (!_shouldIgnore(response.requestOptions)) {
      String? json;
      if (response.data != null) {
        json = fmt(response.data, 1);
        json = 'Return Data: $json';
      } else {
        json = 'response.data 不存在';
      }
      dynamic params;
      if (response.requestOptions.method == 'GET') {
        params = response.requestOptions.queryParameters;
      } else {
        params = response.requestOptions.data;
      }
      LogD(
        "✅ ${response.requestOptions.baseUrl} \n"
        "✅ ${response.requestOptions.path} \n"
        "✅ METHOD:${response.requestOptions.method} \n"
        "✅ HEADER:${fmt(response.requestOptions.headers, 1)} \n"
        "✅ Body:${fmt(params, 1)}\n"
        "$json",
      );
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (!_shouldIgnore(err.requestOptions)) {
      dynamic params;
      if (err.requestOptions.method == 'GET') {
        params = err.requestOptions.queryParameters;
      } else {
        params = err.requestOptions.data;
      }
      LogE("❌ ${err.requestOptions.baseUrl} \n"
          "❌ ${err.requestOptions.path} \n"
          "❌ METHOD:${err.requestOptions.method} \n"
          "❌ HEADER:${fmt(err.requestOptions.headers, 1)} \n"
          "❌ Body:${fmt(params, 1)}\n"
          "❌ Error: ${err.error}\n"
          "❌ Error Type: ${err.type}\n"
          "❌ Error Message: ${err.message}\n"
          "❌ Stack Trace: ${err.stackTrace}");
    }
    super.onError(err, handler);
  }

  bool _shouldIgnore(RequestOptions options) {
    for (var url in ignoredUrls) {
      if (options.path.contains(url)) {
        return true;
      }
    }
    return false;
  }
}

String fmt(dynamic json, int indent) {
  const encoder = JsonEncoder.withIndent('  ');
  return encoder.convert(json);
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/features/contract/domain/models/date_filter.dart';
import 'package:gp_stock_app/features/contract/domain/models/transaction_type.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/models/assets_records/assets_records.dart';
import '../../domain/repository/contract_repository.dart';

part 'fund_records_state.dart';

@injectable
class FundRecordsCubit extends Cubit<FundRecordsState> {
  final ContractRepository _contractService;

  FundRecordsCubit(this._contractService) : super(FundRecordsState());

  Future<void> getAssetRecord({
    required int? contractId,
    bool isLoadMore = false,
    String? commentAssetId,
  }) async {
    if (!isLoadMore) {
      emit(state.copyWith(assetsRecordsFetchStatus: DataStatus.loading));
    }
    int page = isLoadMore ? (state.assetsRecords?.data?.current ?? 0) + 1 : 1;
    try {
      final result = await _contractService.getAssetRecord(
        contractId: contractId,
        pageNum: page,
        commentAssetId: commentAssetId,
        fromType: state.transactionType?.type,
        startTime: state.dateFilter?.startTime,
        endTime: state.dateFilter?.endTime,
      );

      if (!result.isSuccess) {
        emit(state.copyWith(
          assetsRecordsFetchStatus: DataStatus.failed,
          error: result.error,
        ));
        return;
      }

      final records = isLoadMore
          ? [...?state.assetsRecords?.data?.records, ...?result.data?.data?.records]
          : result.data?.data?.records ?? [];

      emit(state.copyWith(
        assetsRecordsFetchStatus: DataStatus.success,
        assetsRecords: result.data?.copyWith(
          data: AssetsRecordsData(
            records: records,
            current: result.data?.data?.current ?? 0,
            hasNext: result.data?.data?.hasNext ?? false,
            total: result.data?.data?.total ?? 0,
          ),
        ),
      ));
    } on Exception catch (e) {
      emit(state.copyWith(
        assetsRecordsFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  Future<void> getContractAccountRecord({
    required int? contractId,
    bool isLoadMore = false,
  }) async {
    if (!isLoadMore) {
      emit(state.copyWith(assetsRecordsFetchStatus: DataStatus.loading));
    }
    int page = isLoadMore ? (state.assetsRecords?.data?.current ?? 0) + 1 : 1;
    try {
      final result = await _contractService.getContractAccountRecord(contractId: contractId, pageNum: page);

      if (!result.isSuccess) {
        emit(state.copyWith(
          assetsRecordsFetchStatus: DataStatus.failed,
          error: result.error,
        ));
        return;
      }

      final records = isLoadMore
          ? [...?state.assetsRecords?.data?.records, ...?result.data?.data?.records]
          : result.data?.data?.records ?? [];

      emit(state.copyWith(
        assetsRecordsFetchStatus: DataStatus.success,
        assetsRecords: result.data?.copyWith(
          data: AssetsRecordsData(
            records: records,
            current: result.data?.data?.current ?? 0,
            hasNext: result.data?.data?.hasNext ?? false,
            total: result.data?.data?.total ?? 0,
          ),
        ),
      ));
    } on Exception catch (e) {
      emit(state.copyWith(
        assetsRecordsFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  void setTransactionType(TransactionType? type) => emit(state.copyWith(transactionType: () => type));
  void setDateFilter(DateFilter? filter) => emit(state.copyWith(dateFilter: () => filter));
}

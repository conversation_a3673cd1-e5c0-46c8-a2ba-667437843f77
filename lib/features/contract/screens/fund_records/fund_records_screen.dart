import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/contract/domain/filter_constants.dart';
import 'package:gp_stock_app/features/contract/widgets/filter_sheet.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../../../../shared/constants/enums.dart';
import '../../../../shared/widgets/flip_text.dart';
import '../../../account/widgets/table_empty.dart';
import '../../domain/models/assets_records/assets_records.dart';
import '../../logic/fund_records/fund_records_cubit.dart';

class FundRecordsScreen extends StatefulWidget {
  final int? contractId;
  final bool isContractAccount;
  const FundRecordsScreen({super.key, required this.contractId, this.isContractAccount = false});

  @override
  State<FundRecordsScreen> createState() => _FundRecordsScreenState();
}

class _FundRecordsScreenState extends State<FundRecordsScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    Helper.afterInit(_init);
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200 && !_isLoadingMore) {
      _loadMore();
    }
  }

  Future<void> _init() async {
    if (widget.isContractAccount) {
      context.read<FundRecordsCubit>().getContractAccountRecord(contractId: widget.contractId);
    } else {
      final commentAssetId = context.read<AccountInfoCubit>().state.accountInfo?.assetId;
      context
          .read<FundRecordsCubit>()
          .getAssetRecord(contractId: widget.contractId, commentAssetId: commentAssetId?.toString());
    }
  }

  Future<void> _onRefresh() async {
    if (widget.isContractAccount) {
      await context.read<FundRecordsCubit>().getContractAccountRecord(contractId: widget.contractId);
    } else {
      final commentAssetId = context.read<AccountInfoCubit>().state.accountInfo?.assetId;
      await context
          .read<FundRecordsCubit>()
          .getAssetRecord(contractId: widget.contractId, commentAssetId: commentAssetId?.toString());
    }
  }

  Future<void> _loadMore() async {
    if (_isLoadingMore) return;

    final state = context.read<FundRecordsCubit>().state;
    final hasNext = state.assetsRecords?.data?.hasNext ?? false;

    if (!hasNext) return; // No more data to load

    setState(() {
      _isLoadingMore = true;
    });

    try {
      if (widget.isContractAccount) {
        await context.read<FundRecordsCubit>().getContractAccountRecord(
              contractId: widget.contractId,
              isLoadMore: true,
            );
      } else {
        final commentAssetId = context.read<AccountInfoCubit>().state.accountInfo?.assetId;
        await context.read<FundRecordsCubit>().getAssetRecord(
              contractId: widget.contractId,
              isLoadMore: true,
              commentAssetId: commentAssetId?.toString(),
            );
      }
    } finally {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: context.theme.cardColor,
        surfaceTintColor: Colors.transparent,
        title: Text('fundRecords'.tr()),
        actions: [
          IconButton(
            onPressed: () {
              showModalBottomSheet(
                context: context,
                backgroundColor: context.theme.cardColor,
                isScrollControlled: true,
                showDragHandle: true,
                builder: (_) => MultiBlocProvider(
                  providers: [
                    BlocProvider.value(
                      value: context.read<FundRecordsCubit>(),
                    ),
                    BlocProvider.value(
                      value: context.read<AccountCubit>(),
                    ),
                  ],
                  child: FundRecordFilterSheet(),
                ),
              );
            },
            icon: Icon(LucideIcons.filter),
          ),
        ],
      ),
      body: BlocBuilder<FundRecordsCubit, FundRecordsState>(
        builder: (context, state) {
          return Padding(
            padding: EdgeInsets.all(10.gr),
            child: _buildTableContent(state),
          );
        },
      ),
    );
  }

  Widget _buildTableContent(FundRecordsState state) {
    if (state.assetsRecordsFetchStatus == DataStatus.loading && (state.assetsRecords?.data?.records?.isEmpty ?? true)) {
      return _buildLoadingList();
    }

    if (state.assetsRecords?.data?.records?.isEmpty ?? true) {
      return _buildEmptyState();
    }

    return SizedBox(
      height: .82.gsh,
      child: RefreshIndicator.adaptive(
        backgroundColor: context.theme.cardColor,
        onRefresh: _onRefresh,
        child: ListView.separated(
          controller: _scrollController,
          padding: EdgeInsets.symmetric(vertical: 8.gw),
          physics: const AlwaysScrollableScrollPhysics(),
          itemCount: (state.assetsRecords?.data?.records?.length ?? 0) + 1, // +1 for loading indicator or end message
          itemBuilder: (context, index) {
            // If we've reached the end of the list
            if (index == (state.assetsRecords?.data?.records?.length ?? 0)) {
              // Check if we're loading more or if there's no more data
              if (_isLoadingMore) {
                return Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.gw),
                  child: const Center(child: CircularProgressIndicator.adaptive()),
                );
              } else if (state.assetsRecords?.data?.hasNext ?? false) {
                // If there's more data but we're not currently loading, show a button to load more
                return Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.gw),
                  child: Center(
                    child: TextButton(
                      onPressed: _loadMore,
                      child: Text('loadMore'.tr()),
                    ),
                  ),
                );
              } else {
                // If there's no more data, show a message
                return Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.gw),
                  child: Center(
                    child: Text(
                      'noMoreData'.tr(),
                      style: context.textTheme.regular.w600,
                    ),
                  ),
                );
              }
            }

            // Regular item
            final record = state.assetsRecords?.data?.records?[index];
            return _FundRecordCard(record: record, isContract: widget.isContractAccount);
          },
          separatorBuilder: (context, index) => 8.verticalSpace,
        ),
      ),
    );
  }

  Widget _buildLoadingList() {
    return Column(
      children: List.generate(
        6,
        (_) => Padding(
          padding: EdgeInsets.only(bottom: 10.gw),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.gr),
            child: ShimmerWidget(
              height: 80.gw,
              width: double.infinity,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return TableEmptyWidget();
  }
}

class _FundRecordCard extends StatelessWidget {
  final RecordData? record;
  final bool isContract;
  const _FundRecordCard({required this.record, required this.isContract});

  @override
  Widget build(BuildContext context) {
    /// 标题，这是一个枚举值，根据枚举值显示对应文字
    String transactionType = _getTransactionTypeText(record?.fromType, isContract: isContract);

    return ShadowBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                transactionType,
                style: context.textTheme.regular.w600,
              ),
              Text(
                record?.createTime ?? '',
                style: context.textTheme.regular.fs9,
              ),
            ],
          ),
          12.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('beforeChangeAmount'.tr(), style: context.textTheme.regular),
              FlipText(
                record?.beforeNum ?? 0,
                style: context.textTheme.primary.w600.ffAkz,
                suffix: ' ${record?.currency ?? ''}',
              ),
            ],
          ),
          8.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('changeAmount'.tr(), style: context.textTheme.regular),
              FlipText(
                record?.updateNum ?? 0,
                style: context.textTheme.primary.w600.ffAkz
                    .copyWith(color: (record?.updateNum ?? 0).getValueColor(context)),
                suffix: ' ${record?.currency ?? ''}',
                prefix: (record?.updateNum ?? 0) < 0 ? '' : '+',
              ),
            ],
          ),
          8.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('afterChangeAmount'.tr(), style: context.textTheme.regular),
              FlipText(
                record?.afterNum ?? 0,
                style: context.textTheme.primary.w600.ffAkz,
                suffix: record?.currency ?? "",
              ),
            ],
          ),
          8.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('serialNumber'.tr(), style: context.textTheme.regular),
              Text(
                record?.serialNo ?? '',
                style: context.textTheme.primary.w600.ffAkz.copyWith(
                  color: context.colorTheme.stockRed,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getTransactionTypeText(int? fromType, {required bool isContract}) {
    if (isContract) {
      return _getTransactionTypeTextForContract(fromType);
    } else {
      return _getTransactionTypeTextForAccount(fromType);
    }
  }

  String _getTransactionTypeTextForAccount(int? fromType) =>
      FilterConstants.contractTypes.firstWhereOrNull((element) => element.type == fromType.toString())?.label.tr() ??
      'transaction'.tr();

  String _getTransactionTypeTextForContract(int? fromType) {
    return switch (fromType) {
      0 => 'all'.tr(),
      1 => 'contract_deposit'.tr(),
      2 => 'margin_increase'.tr(),
      3 => 'transfer_to_spot'.tr(),
      4 => 'contract_buy'.tr(),
      5 => 'contract_sell'.tr(),
      6 => 'interest_deduction'.tr(),
      7 => 'dividend_payment'.tr(),
      8 => 'admin_balance_adjustment'.tr(),
      9 => 'trading_fee'.tr(),
      10 => 'order_unfreeze'.tr(),
      12 => 'unfreezeOrderAmount'.tr(),
      13 => 'unfreezeOrderFee'.tr(),
      14 => 'freezeOrderAmount'.tr(),
      15 => 'freezeOrderFee'.tr(),
      _ => 'transaction'.tr()
    };
  }
}

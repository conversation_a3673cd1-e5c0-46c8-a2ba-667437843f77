import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';

/// Screen for 403 Forbidden (user blacklisted).
class ProhibitionScreen extends StatelessWidget {
  const ProhibitionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.theme.scaffoldBackgroundColor,
      body: PopScope(
        canPop: false,
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(24.gr),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  Assets.prohibitIcon,
                  width: 180.gw,
                  height: 180.gw,
                  fit: BoxFit.contain,
                ),
                Text(
                  'accessProhibited'.tr(),
                  textAlign: TextAlign.center,
                  style: context.textTheme.stockRed.fs16.w600,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

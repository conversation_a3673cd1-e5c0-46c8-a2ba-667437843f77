import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/logic/f_trade_buy_sell_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/subviews/f_trade_buy_sell_amount_row.dart';

import 'package:gp_stock_app/shared/constants/enums.dart';

import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/app_dropdown.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class FtradeLongShortActionsView extends StatelessWidget {
  final FtradeLongShortActionsController longActionsController;
  final FtradeLongShortActionsController shortActionsController;
  const FtradeLongShortActionsView({
    super.key,
    required this.longActionsController,
    required this.shortActionsController,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 8),
        _TradeSection(controller: longActionsController),
        SizedBox(height: 8),
        _TradeSection(controller: shortActionsController),
      ],
    );
  }
}

/*
============================================================================================================================
_TradeSection
============================================================================================================================
*/

class _TradeSection extends StatelessWidget {
  final FtradeLongShortActionsController controller;

  const _TradeSection({
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final String? selectedTitle;
    if (controller.selectedOptionIdx == null) {
      selectedTitle = null;
    } else {
      selectedTitle = controller.optionTitles.elementAtOrNull(controller.selectedOptionIdx!);
    }
    return Column(
      spacing: 8,
      children: [
        if (controller.needShowOptions && controller.optionTitles.isEmpty)
          AppDropdown<String>(
            hintText: 'noAvailablePosition'.tr(),
            items: [],
            selected: '',
            onChanged: (String? value) {},
          ),
        if (controller.needShowOptions && controller.optionTitles.isNotEmpty)
          _Dropdown(
            titles: controller.optionTitles,
            tradeType: controller.tradeTypeCode(),
            selectedTitle: selectedTitle,
            onCellSelected: controller.onOptionCellSelected ?? (idx) {},
          ),
        ...controller.displayRowInfoList.map((displayRowInfo) {
          return FTradeBuySellAmountRow(
            title: displayRowInfo['title'],
            amount: displayRowInfo['amount'],
            suffix: displayRowInfo['suffix'],
            currency: displayRowInfo['currency'] ?? 'CNY',
            isCurrency: true,
            fractionDigits: displayRowInfo['fractionDigits'] ?? 2,
            showTotalToolTip: displayRowInfo['showTotalToolTip'] ?? false,
            fontSize: 13.gr,
          );
        }),
        CustomMaterialButton(
          height: 30.gw,
          isLoading: controller.isLoadingConfirmBtn,
          onPressed: controller.onConfirmPressed,
          buttonText: controller.confirmTitle,
          isEnabled: controller.confirmEnabled,
          color: controller.isLong ? context.upColor : context.downColor,
          borderColor: controller.isLong ? context.upColor : context.downColor,
          borderRadius: 5.gr,
          textColor: context.theme.cardColor,
          fontSize: 13.gr,
        ),
      ],
    );
  }
}

/*
============================================================================================================================
_Dropdown
============================================================================================================================
*/

class _Dropdown extends StatelessWidget {
  final List<String> titles;
  final int tradeType;
  final String? selectedTitle;
  final ValueChanged<int> onCellSelected;

  const _Dropdown({
    required this.titles,
    required this.tradeType,
    required this.selectedTitle,
    required this.onCellSelected,
  });

  @override
  Widget build(BuildContext context) {
    return AppDropdown<String>(
      hintText: 'selectPosition'.tr(),
      items: titles.map((title) {
        final tradeTypeOption = TradeTypeOption.fromValue(tradeType);
        return DropdownMenuItem<String>(
          value: title,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 5.0, vertical: 3.0),
                  decoration: BoxDecoration(
                    color: tradeTypeOption.color(context),
                    borderRadius: BorderRadius.circular(2.gr),
                  ),
                  child: Text(
                    TradeTypeOption.fromValue(tradeType).text,
                    style: context.textTheme.regular.fs9.copyWith(color: Colors.white),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: context.textTheme.primary.fs11,
                    softWrap: true,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
      selected: selectedTitle,
      onChanged: (value) => onCellSelected(titles.indexOf(value ?? '')),
    );
  }
}

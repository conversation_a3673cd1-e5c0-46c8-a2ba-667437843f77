import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/account_balance_widget.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/buy_sell_section.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/fraction_section.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_limit_widget.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_price_type_dropdown.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_quantity_widget.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_type_dropdown.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';

import '../../../../../shared/constants/enums.dart';
import 'trade_form/trade_direction_section.dart';

class TradingFormSection extends StatelessWidget {
  const TradingFormSection({
    super.key,
    required this.instrument,
  });
  final Instrument instrument;
  @override
  Widget build(BuildContext context) {
    void listenCreateOrder(TradingState state) {
      if (state.createOrderStatus == DataStatus.success || state.createOrderStatus == DataStatus.failed) {
        if (state.createOrderStatus == DataStatus.success) {
          GPEasyLoading.showSuccess(message: 'orderTradeSuccess'.tr());
          context.read<AccountInfoCubit>().getAccountInfo();
          context.read<AccountCubit>()
            ..getOrderList(
              AccountMarketType.currentPositions,
              symbol: instrument.symbol,
              market: instrument.market,
              securityType: instrument.securityType,
              contractId: state.contract?.id,
            )
            ..getOrderList(
              AccountMarketType.orderDetails,
              symbol: instrument.symbol,
              market: instrument.market,
              securityType: instrument.securityType,
              contractId: state.contract?.id,
            );
          context.read<TradingCubit>().updateContract();
          if (state.isFromContractDetails) Navigator.pop(context);
        } else if (state.createOrderStatus == DataStatus.failed) {
          GPEasyLoading.showToast(state.error);
        }
      }
    }

    // final isTimedTrading = context.select((TradingCubit cubit) => cubit.state.isTimedTrading);

    return BlocListener<TradingCubit, TradingState>(
      listenWhen: (previous, current) => previous.createOrderStatus != current.createOrderStatus,
      listener: (context, state) => listenCreateOrder(state),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        spacing: 8,
        children: [
          TradeDirectionSection(marketType: instrument.marketType),
          // if (isTimedTrading)
          //   IndexTradeSection()
          // else ...[
            Row(
              spacing: 8,
              children: [
                TradeTypeDropdown(instrument: instrument),
                TradeLimitWidget(),
              ],
            ),
            Row(
              spacing: 8,
              children: [
                TradePriceTypeDropdown(),
                TradeQuantityWidget(),
              ],
            ),
            AccountBalance(),
            FractionSection(),
            BuySellSection(marketType: instrument.marketType),
          // ]
        ],
      ),
    );
  }
}

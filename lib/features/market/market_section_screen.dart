import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/f_trade_list_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/logic/f_trade_list_cubit.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/features/market/index_trade_screen.dart';
import 'package:gp_stock_app/features/market/stock_screen.dart';
import 'package:gp_stock_app/features/market/watch_list/screens/watch_list_screen.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/constants/enums/trading_mode.dart';
import 'package:gp_stock_app/shared/logic/sys_settings/sys_settings_cubit.dart';

import '../../shared/mixin/animation.dart';
import '../../shared/widgets/tab/common_tab_bar.dart';
import 'logic/market/market_cubit.dart';

class MarketSectionScreen extends StatefulWidget {
  final bool showBackButton;
  final ScrollController? scrollController;
  const MarketSectionScreen({
    super.key,
    this.showBackButton = false,
    this.scrollController,
  });

  @override
  State<MarketSectionScreen> createState() => _MarketSectionScreenState();
}

class _MarketSectionScreenState extends State<MarketSectionScreen>
    with StaggeredAnimation, AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  List<String> tabs = [];

  @override
  void initState() {
    super.initState();
    TradingMode currentMode = AppConfig.instance.tradingMode;
    final sysSettingsState = context.read<SysSettingsCubit>().state;

    sysSettingsState.maybeWhen(
      loaded: (_, config) {
        currentMode = TradingModeExtension.fromIndex(config.tradingMode);
      },
      orElse: () {},
    );

    switch (currentMode) {
      case TradingMode.stock:
        tabs = ['stocks', 'stockIndex', 'watchList'];
        break;
      case TradingMode.futures:
        tabs = ['title_futures', 'watchList'];
        break;
      case TradingMode.stockAndFutures:
        tabs = ['stocks', 'stockIndex', 'title_futures', 'watchList'];
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      body: Column(
        children: [
          BlocSelector<MarketCubit, MarketState, MarketSectionTab>(
            selector: (state) => state.selectedMarketSectionTab,
            builder: (context, state) {
              return Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 4.gw),
                child: CommonTabBar.withAutoKey(
                  tabs.map((tab) => tab.tr()).toList(),
                  style: switch (AppConfig.instance.skinStyle) {
                    AppSkinStyle.kGP || AppSkinStyle.kTemplateA => CommonTabBarStyle.round,
                    AppSkinStyle.kTemplateD => CommonTabBarStyle.trapezoid,
                    _ => CommonTabBarStyle.rectangular,
                  },
                  currentIndex: state.index,
                  onTap: (i) => context.read<MarketCubit>().updateMainHeaderTab(MarketSectionTab.values[i]),
                  padding: EdgeInsets.all(2.gw),
                  backgroundColor: context.theme.cardColor,
                  radius: 8.gr,
                  isScrollable: false,
                  labelPadding: EdgeInsets.symmetric(horizontal: 5.gw),
                ),
              );
            },
          ),
          Expanded(
            child: BlocSelector<MarketCubit, MarketState, MarketSectionTab>(
                selector: (state) => state.selectedMarketSectionTab,
                builder: (context, state) => switch (state) {
                      MarketSectionTab.stock => StockScreen(
                          showBackButton: true,
                          scrollController: widget.scrollController,
                        ),
                      MarketSectionTab.stockIndex => IndexTradeScreen(),
                      MarketSectionTab.futures => MultiBlocProvider(
                          providers: [
                            BlocProvider<FTradeListCubit>(
                              create: (_) => FTradeListCubit(FTradeListRepository(), showInHomePage: false),
                            ),
                            BlocProvider<MainCubit>(create: (_) => getIt<MainCubit>()),
                          ],
                          child: FTradeListScreen(showInHomePage: false),
                        ),
                      MarketSectionTab.watchList => WatchListScreen(),
                    }),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart' show Color;

/// ChartColors
///
/// Note:
/// If you need to apply multi theme, you need to change at least the colors related to the text, border and background color
/// Ex:
/// Background: bgColor, selectFillColor
/// Border
/// Text
///
class ChartColors {
  /// the background color of base chart
  Color bgColor;

  Color kLineColor;

  ///
  Color lineFillColor;

  ///
  Color lineFillInsideColor;

  /// color: ma5, ma10, ma30, up, down, vol, macd, diff, dea, k, d, j, rsi
  Color ma5Color;
  Color ma10Color;
  Color ma30Color;
  Color ma60Color;
  Color upColor;
  Color dnColor;
  Color volColor;

  Color macdColor;
  Color difColor;
  Color deaColor;

  Color kColor;
  Color dColor;
  Color jColor;
  Color rsiColor;

  /// default text color: apply for text at grid
  Color defaultTextColor;

  /// color of the current price
  Color nowPriceUpColor;
  Color nowPriceDnColor;
  Color nowPriceTextColor;

  /// trend color
  Color trendLineColor;

  /// depth color
  Color depthBuyColor; //upColor
  Color depthBuyPathColor;
  Color depthSellColor; //dnColor
  Color depthSellPathColor;

  ///value border color after selection
  Color selectBorderColor;

  ///background color when value selected
  Color selectFillColor;

  ///color of grid
  Color gridColor;

  ///color of annotation content
  Color infoWindowNormalColor;
  Color infoWindowTitleColor;
  Color infoWindowUpColor;
  Color infoWindowDnColor;

  /// color of the horizontal cross line
  Color hCrossColor;

  /// color of the vertical cross line
  Color vCrossColor;

  /// text color
  Color crossTextColor;

  ///The color of the maximum and minimum values in the current display
  Color maxColor;
  Color minColor;

  /// get MA color via index
  Color getMAColor(int index) {
    switch (index % 3) {
      case 1:
        return ma10Color;
      case 2:
        return ma30Color;
      case 3:
        return ma60Color;
      default:
        return ma5Color;
    }
  }

  /// constructor chart color
  ChartColors({
    this.bgColor = const Color(0xffffffff),
    this.kLineColor = const Color(0xff4C86CD),

    ///
    this.lineFillColor = const Color(0x554C86CD),

    ///
    this.lineFillInsideColor = const Color(0x00000000),

    ///
    this.ma5Color = const Color(0xffE5B767),
    this.ma10Color = const Color(0xff1FD1AC),
    this.ma30Color = const Color(0xffB48CE3),
    this.ma60Color = const Color(0xFFD5405D),
    this.upColor = const Color(0xFF14AD8F),
    this.dnColor = const Color(0xFFD5405D),
    this.volColor = const Color(0xff2f8fd5),
    this.macdColor = const Color(0xff2f8fd5),
    this.difColor = const Color(0xffE5B767),
    this.deaColor = const Color(0xff1FD1AC),
    this.kColor = const Color(0xffE5B767),
    this.dColor = const Color(0xff1FD1AC),
    this.jColor = const Color(0xffB48CE3),
    this.rsiColor = const Color(0xffE5B767),
    this.defaultTextColor = const Color(0xFF909196),
    this.nowPriceUpColor = const Color(0xFF14AD8F),
    this.nowPriceDnColor = const Color(0xFFD5405D),
    this.nowPriceTextColor = const Color(0xffffffff),

    /// trend color
    this.trendLineColor = const Color(0xFFF89215),

    ///depth color
    this.depthBuyColor = const Color(0xFF14AD8F),
    this.depthBuyPathColor = const Color(0x3314AD8F),
    this.depthSellColor = const Color(0xFFD5405D),
    this.depthSellPathColor = const Color(0x33D5405D),

    ///value border color after selection
    this.selectBorderColor = const Color(0xFF222223),

    ///background color when value selected
    this.selectFillColor = const Color(0xffffffff),

    ///color of grid
    this.gridColor = const Color(0xFFD1D3DB),

    ///color of annotation content
    this.infoWindowNormalColor = const Color(0xFF222223),
    this.infoWindowTitleColor = const Color(0xFF4D4D4E), //0xFF707070
    this.infoWindowUpColor = const Color(0xFF14AD8F),
    this.infoWindowDnColor = const Color(0xFFD5405D),
    this.hCrossColor = const Color(0xFF222223),
    this.vCrossColor = const Color(0x28424652),
    this.crossTextColor = const Color(0xFF222223),

    ///The color of the maximum and minimum values in the current display
    this.maxColor = const Color(0xFF222223),
    this.minColor = const Color(0xFF222223),
  });
}

class ChartStyle {
  double topPadding = 30.0;

  double bottomPadding = 20.0;

  double childPadding = 12.0;

  ///point-to-point distance
  double pointWidth = 11.0;

  ///candle width
  double candleWidth = 8.5;
  double candleLineWidth = 1.0;

  ///vol column width
  double volWidth = 8.5;

  ///macd column width
  double macdWidth = 1.2;

  ///vertical-horizontal cross line width
  double vCrossWidth = 8.5;
  double hCrossWidth = 0.5;

  ///(line length - space line - thickness) of the current price
  double nowPriceLineLength = 4.5;
  double nowPriceLineSpan = 3.5;
  double nowPriceLineWidth = 1;

  int gridRows = 4;
  int gridColumns = 4;

  ///customize the time below
  List<String>? dateTimeFormat;

  ///custom intraday time labels (e.g., ["9:30", "11:30/13:00", "15:00"])
  List<String>? customIntradayLabels;

  ///market type for positioning logic (CN, HK, US)
  String? marketType;

  /// Default constructor with hardcoded values (for backward compatibility)
  ChartStyle();

  /// Factory constructor for responsive sizing based on screen width
  /// Uses mathematical formulas to scale chart elements smoothly across all screen sizes
  factory ChartStyle.responsive(double screenWidth) {
    // Reference width for base calculations (iPhone 14 Pro width)
    const double referenceWidth = 393.0;

    // Calculate scaling factor using logarithmic curve for smooth scaling
    // This ensures elements don't become too small on small screens or too large on big screens
    final double scaleFactor = _calculateScaleFactor(screenWidth, referenceWidth);

    final style = ChartStyle();

    // Apply responsive scaling to chart elements
    style.pointWidth = _scaleWithBounds(11.0 * scaleFactor, min: 6.0, max: 18.0);
    style.candleWidth = _scaleWithBounds(8.5 * scaleFactor, min: 4.0, max: 15.0);
    style.volWidth = _scaleWithBounds(8.5 * scaleFactor, min: 4.0, max: 15.0);
    style.vCrossWidth = _scaleWithBounds(8.5 * scaleFactor, min: 4.0, max: 15.0);

    // Scale other elements proportionally but with tighter bounds
    style.macdWidth = _scaleWithBounds(1.2 * scaleFactor, min: 0.8, max: 2.0);
    style.hCrossWidth = _scaleWithBounds(0.5 * scaleFactor, min: 0.3, max: 1.0);
    style.candleLineWidth = _scaleWithBounds(1.0 * scaleFactor, min: 0.5, max: 1.5);

    // Scale line elements for current price
    style.nowPriceLineLength = _scaleWithBounds(4.5 * scaleFactor, min: 3.0, max: 7.0);
    style.nowPriceLineSpan = _scaleWithBounds(3.5 * scaleFactor, min: 2.0, max: 5.5);
    style.nowPriceLineWidth = _scaleWithBounds(1.0 * scaleFactor, min: 0.5, max: 1.5);

    return style;
  }

  /// Calculate scale factor using a smooth mathematical curve
  /// Uses a combination of linear and logarithmic scaling for optimal visual results
  static double _calculateScaleFactor(double screenWidth, double referenceWidth) {
    if (screenWidth <= 0) return 1.0;

    // Base linear scaling
    double linearScale = screenWidth / referenceWidth;

    // Apply logarithmic dampening to prevent extreme scaling
    // This creates a smooth curve that scales more aggressively on smaller screens
    // and less aggressively on larger screens
    double logScale = 1.0 + (linearScale - 1.0) * 0.7;

    // Ensure reasonable bounds
    return logScale.clamp(0.6, 1.8);
  }

  /// Apply scaling with minimum and maximum bounds
  static double _scaleWithBounds(double value, {required double min, required double max}) {
    return value.clamp(min, max);
  }
}
